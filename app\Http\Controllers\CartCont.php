<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use App\Models\Coupon;
use App\Models\cartMod;
use App\Models\Product_list;
use App\Models\Color;
use App\Models\Size;
use App\Models\Subcategory;
use App\Models\WishTable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

class CartCont extends Controller
{
    // === Add Cart ===
    function add_cart(Request $request){
        $request->validate([
            'prod_color' => 'required',
            'prod_size' => 'required',
            'quantity' => 'required',
            'customer_picture' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
        ], [
            'prod_color' => 'Please Select One Color!',
            'prod_size' => 'Please Select One Available Size!!',
            'quantity' => 'This Size currently Out of Stock!',
            'customer_picture.image' => 'Please upload a valid image file!',
            'customer_picture.mimes' => 'Only JPG, PNG, and GIF files are allowed!',
            'customer_picture.max' => 'Image size must be less than 5MB!',
        ]);

        if(Auth::guard('cust_login')->check()){
            // Registered user - use database cart

            // Handle customer picture upload
            $customerPicturePath = null;
            if ($request->hasFile('customer_picture')) {
                $file = $request->file('customer_picture');
                $fileName = 'customer_' . Auth::guard('cust_login')->id() . '_' . time() . '.' . $file->getClientOriginalExtension();

                // Create directory if it doesn't exist
                $uploadPath = public_path('uploads/customer_pictures');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                // Move the file
                $file->move($uploadPath, $fileName);
                $customerPicturePath = $fileName;
            }

            $dupli_cart = cartMod::where('customer_id', Auth::guard('cust_login')->id())->where('product_id', $request->product_id)->where('color_id', $request->prod_color)->where('size_id', $request->prod_size);

            // Check if item_type is provided and matches for duplicate check
            if($request->has('item_type') && $request->item_type) {
                $dupli_cart = $dupli_cart->where('item_type', $request->item_type);
            }

            // Check if customer_picture is provided and matches for duplicate check
            if($customerPicturePath) {
                $dupli_cart = $dupli_cart->where('customer_picture', $customerPicturePath);
            }

            if($dupli_cart->exists()){
                $dupli_cart->increment('quantity', $request->quantity);
                $message = 'Item(s) Added to Cart!';
            }
            else {
                cartMod::insert([
                    'customer_id' => Auth::guard('cust_login')->id(),
                    'product_id' => $request->product_id,
                    'color_id' => $request->prod_color,
                    'size_id' => $request->prod_size,
                    'quantity' => $request->quantity,
                    'item_type' => $request->item_type ?? null,
                    'customer_picture' => $customerPicturePath,
                    'created_at' => Carbon::now(),
                ]);
                $message = 'Item(s) Added to Cart!';
            }

            // Return JSON response for AJAX requests
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'cart_count' => cartMod::where('customer_id', Auth::guard('cust_login')->id())->count()
                ]);
            }

            return back()->with('cart_added', $message);
        }
        else {
            // Guest user - use session cart
            $cart = session()->get('guest_cart', []);

            // Handle customer picture upload for guests
            $customerPicturePath = null;
            if ($request->hasFile('customer_picture')) {
                $file = $request->file('customer_picture');
                $fileName = 'guest_' . session()->getId() . '_' . time() . '.' . $file->getClientOriginalExtension();

                // Create directory if it doesn't exist
                $uploadPath = public_path('uploads/customer_pictures');
                if (!file_exists($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                // Move the file
                $file->move($uploadPath, $fileName);
                $customerPicturePath = $fileName;
            }

            // Create unique key for cart item
            $cartKey = $request->product_id . '_' . $request->prod_color . '_' . $request->prod_size;
            if ($request->has('item_type') && $request->item_type) {
                $cartKey .= '_' . $request->item_type;
            }
            if ($customerPicturePath) {
                $cartKey .= '_' . $customerPicturePath;
            }

            // Check if item already exists in cart
            if (isset($cart[$cartKey])) {
                $cart[$cartKey]['quantity'] += $request->quantity;
            } else {
                $cart[$cartKey] = [
                    'product_id' => $request->product_id,
                    'color_id' => $request->prod_color,
                    'size_id' => $request->prod_size,
                    'quantity' => $request->quantity,
                    'item_type' => $request->item_type ?? null,
                    'customer_picture' => $customerPicturePath,
                    'created_at' => now()->toDateTimeString(),
                ];
            }

            session()->put('guest_cart', $cart);
            $message = 'Item(s) Added to Cart!';

            // Return JSON response for AJAX requests
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'cart_count' => count($cart)
                ]);
            }

            return back()->with('cart_added', $message);
        }
    }

    // === Remove Cart [Master] ===
    function remove_cart($card_id){
        cartMod::find($card_id)->delete();
        return back()->with('cart_removed', 'Item Removed from Cart');
    }

    // === Remove All Cart ===
    function remove_all_cart(){
        cartMod::where('customer_id', Auth::guard('cust_login')->id())->delete();
        return back()->with('cart_removed', 'All items Removed from Cart');
    }

    // === Add Wishlist ===
    function add_wishlist(Request $request){
        if(Auth::guard('cust_login')->check()){
            $dupli_cart = WishTable::where('customer_id', Auth::guard('cust_login')->id())->where('product_id', $request->product_id)->where('color_id', $request->prod_color)->where('size_id', $request->prod_size);

            if($dupli_cart->exists()){
                $dupli_cart->increment('quantity', $request->quantity);
                return back()->with('wish_added', 'Item(s) Added to Wishlist!');
            }
            else {
                WishTable::insert([
                    'customer_id' => Auth::guard('cust_login')->id(),
                    'product_id' => $request->product_id,
                    'color_id' => $request->prod_color,
                    'size_id' => $request->prod_size,
                    'quantity' => $request->quantity,
                    'created_at' => Carbon::now(),
                ]);
                return back()->with('wish_added', 'Item(s) Added to Wishlist!');
            }
        }
        else {
            return redirect()->route('customer_login')->with('cart_login', 'Please Login/Register to use Cart');
        }
    }

    // === Remove Wishlist ===
    function remove_wishlist($wish_id){
        WishTable::find($wish_id)->delete();
        return back()->with('wish_removed', 'Item Removed from Wishlist!');
    }

    // === Remove Btn_Wishlist ===
    function remove_btn_wishlist($product_id){
        $test = WishTable::where('customer_id', Auth::guard('cust_login')->id())->where('product_id', $product_id)->delete();
        return back()->with('wish_removed', 'Item(s) Removed from Wishlist!');
    }

    // === Remove All Wishlist ===
    function remove_all_wishlist(){
        WishTable::where('customer_id', Auth::guard('cust_login')->id())->delete();
        return back()->with('wish_removed', 'Item(s) Removed from Wishlist!');
    }

    // === Cart Page ===
    function cart_store_update(Request $request){
        $cart_info = collect();
        $is_guest = false;

        if(Auth::guard('cust_login')->check()){
            // Registered user - get cart from database
            $cart_info = cartMod::where('customer_id', Auth::guard('cust_login')->id())->get();
        } else {
            // Guest user - get cart from session
            $is_guest = true;
            $guest_cart = session()->get('guest_cart', []);

            // Convert session cart to collection with product relationships
            foreach ($guest_cart as $key => $item) {
                $product = Product_list::find($item['product_id']);
                $color = Color::find($item['color_id']);
                $size = Size::find($item['size_id']);

                if ($product && $color && $size) {
                    $cart_item = (object) [
                        'id' => $key,
                        'product_id' => $item['product_id'],
                        'color_id' => $item['color_id'],
                        'size_id' => $item['size_id'],
                        'quantity' => $item['quantity'],
                        'item_type' => $item['item_type'],
                        'customer_picture' => $item['customer_picture'],
                        'created_at' => $item['created_at'],
                        'relto_product' => $product,
                        'relto_color' => $color,
                        'relto_size' => $size,
                    ];
                    $cart_info->push($cart_item);
                }
            }
        }

            $coupon_info = Coupon::where('coupon_name', $request->coupon)->get()->first();
            $coupon = null;
            $discount = null;
            $min_total = null;
            $least_disc = null;
            $most_disc = null;
            $btn = $request->coupon_btn;


            if($request->coupon != null){
                if(Coupon::where('coupon_name', $request->coupon)->exists()){
                    if($coupon_info->validity > carbon::now()->isoFormat('YYYY-MM-DD')){
                        if($coupon_info->min_total != null){
                            $min_total = $coupon_info->min_total;
                        }
                        if($coupon_info->least_disc != null){
                            $least_disc = $coupon_info->least_disc;
                        }
                        if($coupon_info->most_disc != null){
                            $most_disc = $coupon_info->most_disc;
                        }

                        if($request->total >= $min_total){

                            // === Percent-Off ===
                            if($coupon_info->type == 1){
                                
                                $discount = $request->total * ($coupon_info->discount/100);
                                $coupon = 'Discount: '.$coupon_info->discount.'% ('.$least_disc.' - '.$most_disc.' Tk.)';
                                
                                if($discount > $most_disc){
                                    $discount = $most_disc;
                                }
                                else if($discount < $least_disc){
                                    $discount = $least_disc;
                                }
                            }

                            // === Solid ===
                            else if($coupon_info->type == 2){
                                $coupon = 'Coupon Applied: '.$coupon_info->discount.' Tk.';
                                $discount = $coupon_info->discount;
                            }

                            // === Category ===
                            else if($coupon_info->type == 10){
                                $sub_list = explode(",", $coupon_info->subcata);
                                $coupon = 'Invalid Cart Item';

                                foreach ($sub_list as $sub){
                                    foreach ($cart_info as $cart){
                                        if($cart->relto_product->subcata_id == $sub){
                                            $discount += $cart->relto_product->after_disc * ($coupon_info->discount/100) * $cart->quantity;
                                            $coupon = 'Discount: '.$coupon_info->discount.'% ('.$least_disc.' - '.$most_disc.' Tk.)';
                                            
                                            if($most_disc != null && $discount > $most_disc){
                                                $discount = $most_disc;
                                            }
                                            else if($least_disc != null && $discount < $least_disc){
                                                $discount = $least_disc;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        else{
                            $discount = 0;
                            $coupon = 'Purchase minimum'. $min_total;
                        }
                    }
                    else{
                        $discount = 0;
                        $coupon = 'Coupon Validity Expired!';
                    }
                }
                else{
                    $discount = 0;
                    $coupon = 'Invalid Coupon!';
                }
            }
            else{
                $discount = 0;
                $coupon = 'No Coupon Applied!';
            }

        return view('frontend.cart', [
            'cart_info' => $cart_info,
            'coupon' => $coupon,
            'discount' => $discount,
            'btn' => $btn,
            'is_guest' => $is_guest,
        ]);
    }

    // === Cart Updated New===
    function cart_updated(Request $request){
        foreach($request->qty_new as $cart_id=>$qty_new){

            cartMod::find($cart_id)->update([
                'quantity' => $qty_new,
            ]);
        }
        return back()->with('cart_upd', 'Cart Updated Successfully!');
    }

    // === Remove Cart from Cart-Page ===
    function remove_cart_page($card_id){
        cartMod::find($card_id)->delete();
        return back()->with('cart_page_removed', 'Item Removed from Cart');
    }

    // === Remove Guest Cart Item ===
    public function remove_guest_cart_item(Request $request){
        $cart = session()->get('guest_cart', []);

        if (isset($cart[$request->item_key])) {
            unset($cart[$request->item_key]);
            session()->put('guest_cart', $cart);

            return response()->json([
                'success' => true,
                'message' => 'Item removed from cart',
                'cart_count' => count($cart)
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Item not found in cart'
        ]);
    }

    // === Clear Guest Cart ===
    public function clear_guest_cart(Request $request){
        session()->forget('guest_cart');

        return response()->json([
            'success' => true,
            'message' => 'Cart cleared',
            'cart_count' => 0
        ]);
    }
}
